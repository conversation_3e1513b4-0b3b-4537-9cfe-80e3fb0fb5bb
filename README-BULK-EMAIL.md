# Hệ thống gửi Email hàng loạt với SendGrid

## Cài đặt và cấu hình

### 1. <PERSON><PERSON>u hình SendGrid API Key

Thêm SendGrid API Key vào file `.env`:

```env
MAIL_MAILER=sendgrid
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Your Company Name"
SENDGRID_API_KEY=your_sendgrid_api_key_here
```

### 2. Chạy migration để tạo bảng email logs

```bash
php artisan migrate
```

### 3. Cấu hình danh sách email

Chỉnh sửa file `config/bulk-emails.php` và thêm danh sách email:

```php
'emails' => [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    // Thêm email khác...
],
```

### 4. Tạo HTML template

Tạo file HTML template trong thư mục `resources/views/email-templates/`

<PERSON><PERSON> dụ: `resources/views/email-templates/welcome.html`

<PERSON><PERSON><PERSON> có thể sử dụng các biến trong template:
- `{{company_name}}`
- `{{website_url}}`
- `{{support_email}}`
- `{{current_year}}`

## Cách sử dụng

### Gửi email hàng loạt

```bash
php artisan email:send-bulk welcome.html "Welcome to our platform!"
```

### Xem thống kê

```bash
php artisan email:send-bulk welcome.html "Subject" --stats
```

### Reset logs (để test lại)

```bash
php artisan email:send-bulk welcome.html "Subject" --reset
```

## Tính năng

✅ **Tự động skip email đã gửi**: Hệ thống sẽ tự động bỏ qua những email đã gửi thành công trước đó

✅ **Log chi tiết**: Lưu trữ trạng thái gửi email (thành công/thất bại) và lý do lỗi

✅ **Rate limiting**: Có delay nhỏ giữa các email để tránh bị giới hạn

✅ **Progress bar**: Hiển thị tiến trình gửi email

✅ **Thống kê**: Xem số lượng email đã gửi, thất bại

✅ **Template variables**: Hỗ trợ thay thế biến trong template HTML

## Cấu trúc thư mục

```
├── app/
│   ├── Console/Commands/SendBulkEmails.php    # Command để chạy gửi email
│   ├── Models/EmailLog.php                    # Model lưu log email
│   └── Services/BulkEmailService.php          # Service xử lý logic gửi email
├── config/
│   └── bulk-emails.php                        # File cấu hình danh sách email
├── resources/views/email-templates/
│   └── welcome.html                           # Template email mẫu
└── database/migrations/
    └── *_create_email_logs_table.php          # Migration tạo bảng logs
```

## Lưu ý

- Đảm bảo SendGrid API Key có quyền gửi email
- Kiểm tra domain sender đã được verify trong SendGrid
- File template HTML phải được đặt trong `resources/views/email-templates/`
- Hệ thống sẽ tự động tạo unique constraint để tránh gửi trùng email cho cùng template
