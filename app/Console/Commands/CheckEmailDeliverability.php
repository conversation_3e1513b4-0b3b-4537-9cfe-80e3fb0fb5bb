<?php

namespace App\Console\Commands;

use App\Services\EmailDeliverabilityService;
use Illuminate\Console\Command;

class CheckEmailDeliverability extends Command
{
    protected $signature = 'email:check-deliverability
                            {template : HTML template file name}
                            {subject : Email subject}';

    protected $description = 'Check email deliverability and spam score';

    private $deliverabilityService;

    public function __construct(EmailDeliverabilityService $deliverabilityService)
    {
        parent::__construct();
        $this->deliverabilityService = $deliverabilityService;
    }

    public function handle()
    {
        $template = $this->argument('template');
        $subject = $this->argument('subject');

        $this->info('=== EMAIL DELIVERABILITY CHECK ===');

        // Load template
        $templatePath = resource_path("views/email-templates/{$template}");
        if (!file_exists($templatePath)) {
            $this->error("Template not found: {$template}");
            return;
        }

        $htmlContent = file_get_contents($templatePath);
        $variables = config('bulk-emails.variables', []);

        // Replace variables
        foreach ($variables as $key => $value) {
            $htmlContent = str_replace("{{" . $key . "}}", $value, $htmlContent);
        }

        // Analyze spam score
        $analysis = $this->deliverabilityService->analyzeSpamScore($subject, $htmlContent);

        $this->info("📧 Template: {$template}");
        $this->info("📝 Subject: {$subject}");
        $this->newLine();

        // Display spam score
        $riskColor = $analysis['risk_level'] === 'LOW' ? 'info' :
                    ($analysis['risk_level'] === 'MEDIUM' ? 'comment' : 'error');

        $this->line("🎯 Spam Score: {$analysis['score']}/10", $riskColor);
        $this->line("⚠️  Risk Level: {$analysis['risk_level']}", $riskColor);

        if (!empty($analysis['triggers'])) {
            $this->error("\n🚨 Spam Triggers Found:");
            foreach ($analysis['triggers'] as $trigger) {
                $this->error("  - {$trigger}");
            }
        } else {
            $this->info("\n✅ No major spam triggers detected!");
        }

        // Check domain authentication
        $this->newLine();
        $this->info('=== DOMAIN AUTHENTICATION ===');
        $domainCheck = $this->deliverabilityService->checkDomainAuth();

        $this->info("Domain: {$domainCheck['domain']}");
        $this->line("SPF: {$domainCheck['spf_status']}");
        $this->line("DKIM: {$domainCheck['dkim_status']}");
        $this->line("DMARC: {$domainCheck['dmarc_status']}");

        // Show recommendations
        $this->newLine();
        $this->info('=== RECOMMENDATIONS ===');
        $tips = $this->deliverabilityService->getDeliverabilityTips();
        foreach ($tips as $tip) {
            $this->line($tip);
        }

        if (!empty($domainCheck['recommendations'])) {
            $this->newLine();
            $this->info('=== DOMAIN SETUP RECOMMENDATIONS ===');
            foreach ($domainCheck['recommendations'] as $rec) {
                $this->line("• {$rec}");
            }
        }
    }
}
