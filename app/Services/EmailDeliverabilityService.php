<?php

namespace App\Services;

class EmailDeliverabilityService
{
    /**
     * Analyze email content for spam triggers
     */
    public function analyzeSpamScore($subject, $htmlContent)
    {
        $spamTriggers = [];
        $score = 0;

        // Subject line analysis
        $spamWords = [
            'FREE', 'URGENT', 'ACT NOW', 'LIMITED TIME', 'CLICK HERE',
            'MAKE MONEY', 'GUARANTEED', 'NO RISK', 'WINNER', 'CONGRATULATIONS',
            'CASH', 'PRIZE', 'OFFER EXPIRES', 'SPECIAL PROMOTION'
        ];

        foreach ($spamWords as $word) {
            if (stripos($subject, $word) !== false) {
                $spamTriggers[] = "Subject contains spam word: {$word}";
                $score += 2;
            }
        }

        // Content analysis
        if (preg_match_all('/!{2,}/', $htmlContent) > 3) {
            $spamTriggers[] = "Too many exclamation marks";
            $score += 1;
        }

        if (preg_match_all('/[A-Z]{5,}/', $htmlContent) > 5) {
            $spamTriggers[] = "Too much CAPS text";
            $score += 2;
        }

        // Image to text ratio
        $imageCount = preg_match_all('/<img[^>]*>/', $htmlContent);
        $textLength = strlen(strip_tags($htmlContent));
        
        if ($imageCount > 0 && $textLength < 200) {
            $spamTriggers[] = "High image to text ratio";
            $score += 3;
        }

        // Missing unsubscribe link
        if (stripos($htmlContent, 'unsubscribe') === false) {
            $spamTriggers[] = "Missing unsubscribe link";
            $score += 2;
        }

        return [
            'score' => $score,
            'triggers' => $spamTriggers,
            'risk_level' => $this->getRiskLevel($score)
        ];
    }

    private function getRiskLevel($score)
    {
        if ($score <= 2) return 'LOW';
        if ($score <= 5) return 'MEDIUM';
        return 'HIGH';
    }

    /**
     * Get recommendations to improve deliverability
     */
    public function getDeliverabilityTips()
    {
        return [
            '✅ Sử dụng domain đã verify trong SendGrid',
            '✅ Thêm SPF, DKIM, DMARC records cho domain',
            '✅ Tránh từ ngữ spam trong subject line',
            '✅ Bao gồm cả HTML và plain text version',
            '✅ Thêm unsubscribe link rõ ràng',
            '✅ Sử dụng tỷ lệ text/image hợp lý',
            '✅ Tránh quá nhiều link trong email',
            '✅ Test email trước khi gửi hàng loạt',
            '✅ Gửi từ IP có reputation tốt',
            '✅ Warm-up IP address trước khi gửi volume lớn'
        ];
    }

    /**
     * Check domain authentication status
     */
    public function checkDomainAuth()
    {
        $domain = $this->extractDomain(config('mail.from.address'));
        
        return [
            'domain' => $domain,
            'spf_status' => $this->checkSPF($domain),
            'dkim_status' => 'Check in SendGrid dashboard',
            'dmarc_status' => $this->checkDMARC($domain),
            'recommendations' => $this->getDomainRecommendations()
        ];
    }

    private function extractDomain($email)
    {
        return substr(strrchr($email, "@"), 1);
    }

    private function checkSPF($domain)
    {
        $records = dns_get_record($domain, DNS_TXT);
        foreach ($records as $record) {
            if (strpos($record['txt'], 'v=spf1') !== false) {
                if (strpos($record['txt'], 'include:sendgrid.net') !== false) {
                    return '✅ SPF configured for SendGrid';
                }
                return '⚠️ SPF exists but may not include SendGrid';
            }
        }
        return '❌ No SPF record found';
    }

    private function checkDMARC($domain)
    {
        $records = dns_get_record('_dmarc.' . $domain, DNS_TXT);
        if (!empty($records)) {
            return '✅ DMARC record exists';
        }
        return '❌ No DMARC record found';
    }

    private function getDomainRecommendations()
    {
        return [
            'Add SPF record: "v=spf1 include:sendgrid.net ~all"',
            'Configure DKIM in SendGrid dashboard',
            'Add DMARC record for domain protection',
            'Verify domain ownership in SendGrid',
            'Use consistent From name and email'
        ];
    }

    /**
     * Generate email preview for testing
     */
    public function generatePreview($htmlContent, $variables = [])
    {
        // Replace variables
        foreach ($variables as $key => $value) {
            $htmlContent = str_replace("{{" . $key . "}}", $value, $htmlContent);
        }

        return $htmlContent;
    }
}
