<?php

namespace App\Console\Commands;

use App\Services\BulkEmailService;
use Illuminate\Console\Command;

class SendVWS2025BulkEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:send-bulk-vws-2025
                            {--template : HTML template file name (e.g., welcome.html)}
                            {--subject : Email subject}
                            {--reset : Reset email logs for this template}
                            {--stats : Show statistics only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send VWS 2025 bulk emails using SendGrid API with HTML templates';

    private $bulkEmailService;

    public function __construct(BulkEmailService $bulkEmailService)
    {
        parent::__construct();
        $this->bulkEmailService = $bulkEmailService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $template = $this->option('template') ?: 'vws-2025-improved.html';
        $subject = $this->option('subject') ?: 'SỰ KIỆN VIETNAM WEB SUMMIT 2025 CHÍNH THỨC BẮT ĐẦU';
        $templateName = basename($template, '.html');

        // Show stats only
        if ($this->option('stats')) {
            $this->showStats($templateName);
            return;
        }

        // Reset logs if requested
        if ($this->option('reset')) {
            if ($this->confirm("Are you sure you want to reset all email logs for template '{$templateName}'?")) {
                $deleted = $this->bulkEmailService->resetLogs($templateName);
                $this->info("Deleted {$deleted} email log records.");
                return;
            }
        }

        // Load email list
        $emails = $this->loadEmailList();
        if (empty($emails)) {
            $this->error('No emails found in the email list!');
            return;
        }

        $this->info("Found " . count($emails) . " emails to process.");
        $this->info("Template: {$template}");
        $this->info("Subject: {$subject}");

        if (!$this->confirm('Do you want to proceed with sending emails?')) {
            $this->info('Email sending cancelled.');
            return;
        }

        // Send emails
        $this->info('Starting bulk email sending...');
        $progressBar = $this->output->createProgressBar(count($emails));
        $progressBar->start();

        try {
            // Load variables from config
            $variables = config('bulk-emails.variables', []);

            $results = $this->bulkEmailService->sendBulkEmails($emails, $template, $subject, $variables);
            $progressBar->finish();
            $this->newLine(2);

            // Display results
            $this->displayResults($results);

        } catch (\Exception $e) {
            $progressBar->finish();
            $this->newLine();
            $this->error('Error: ' . $e->getMessage());
        }
    }

    private function loadEmailList()
    {
        $configPath = config_path('bulk-emails.php');

        if (!file_exists($configPath)) {
            $this->error("Email list config file not found: {$configPath}");
            $this->info("Please create the file with your email list.");
            return [];
        }

        $config = include $configPath;
        return $config['emails'] ?? [];
    }

    private function displayResults(array $results)
    {
        $this->info('=== BULK EMAIL SENDING RESULTS ===');
        $this->info("Total emails: {$results['total']}");
        $this->info("Successfully sent: {$results['sent']}");
        $this->info("Skipped (already sent): {$results['skipped']}");
        $this->info("Failed: {$results['failed']}");

        if (!empty($results['errors'])) {
            $this->error("\nErrors:");
            foreach ($results['errors'] as $error) {
                $this->error("- {$error}");
            }
        }
    }

    private function showStats($templateName)
    {
        $stats = $this->bulkEmailService->getStats($templateName);

        $this->info("=== EMAIL STATISTICS ===");
        if ($templateName) {
            $this->info("Template: {$templateName}");
        }
        $this->info("Total: {$stats['total']}");
        $this->info("Sent: {$stats['sent']}");
        $this->info("Failed: {$stats['failed']}");
    }
}
