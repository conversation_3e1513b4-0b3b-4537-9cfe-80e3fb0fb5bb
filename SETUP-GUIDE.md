# 🚀 Hướng dẫn Setup nhanh - Gửi Email hàng loạt

## ✅ Bước 1: Cấu hình SendGrid API Key

### 1.1. Lấy SendGrid API Key
1. Đăng ký/đăng nhập vào [SendGrid](https://sendgrid.com)
2. Vào **Settings** → **API Keys**
3. Tạo API Key mới với quyền **Full Access** hoặc **Mail Send**
4. Copy API Key

### 1.2. Cấu hình trong Laravel
Thêm vào file `.env`:

```env
SENDGRID_API_KEY=SG.xxxxxxxxxxxxxxxxxx
MAIL_MAILER=sendgrid
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Your Company Name"
```

**⚠️ Lưu ý:** Email FROM phải được verify trong SendGrid!

## ✅ Bước 2: Thêm danh sách email

Chỉnh sửa file `config/bulk-emails.php`:

```php
'emails' => [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    // Thêm email khác...
],
```

## ✅ Bước 3: Test cấu hình

```bash
# Kiểm tra cấu hình
php artisan email:test-config
```

Nếu thấy tất cả ✅ thì bạn đã setup thành công!

## ✅ Bước 4: Gửi email test

```bash
# Gửi email test
php artisan email:send-bulk test.html "🚀 Test Email từ hệ thống"
```

## ✅ Bước 5: Gửi email thật

```bash
# Gửi email với template welcome
php artisan email:send-bulk welcome.html "Chào mừng bạn đến với chúng tôi!"
```

## 🎯 Các lệnh hữu ích

```bash
# Xem thống kê
php artisan email:send-bulk welcome.html "Subject" --stats

# Reset logs để gửi lại
php artisan email:send-bulk welcome.html "Subject" --reset

# Test cấu hình
php artisan email:test-config
```

## 🔧 Troubleshooting

### Lỗi "SendGrid API error"
- Kiểm tra API Key có đúng không
- Kiểm tra email FROM đã verify chưa
- Kiểm tra quyền của API Key

### Lỗi "Template not found"
- Kiểm tra file template có tồn tại trong `resources/views/email-templates/`
- Kiểm tra tên file có đúng không (bao gồm .html)

### Email không nhận được
- Kiểm tra spam folder
- Kiểm tra email FROM đã verify trong SendGrid
- Kiểm tra domain reputation

## 📁 Templates có sẵn

- `test.html` - Template test đơn giản
- `welcome.html` - Template chào mừng với styling đẹp

## 🎨 Tạo template mới

1. Tạo file `.html` trong `resources/views/email-templates/`
2. Sử dụng các biến: `{{company_name}}`, `{{website_url}}`, `{{support_email}}`, `{{current_year}}`
3. Test với lệnh gửi email

## ⚡ Chạy local - Không cần mail server

Hệ thống này chạy hoàn toàn qua SendGrid API, không cần:
- ❌ Mail server local
- ❌ SMTP server
- ❌ Postfix/Sendmail

Chỉ cần:
- ✅ Internet connection
- ✅ SendGrid API Key
- ✅ Laravel project

**Vậy là xong! Bạn có thể gửi email từ máy local ngay lập tức! 🎉**
