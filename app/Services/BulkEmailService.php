<?php

namespace App\Services;

use App\Models\EmailLog;
use Illuminate\Support\Facades\Log;
use SendGrid\Mail\Mail as SendGridMail;
use SendGrid;

class BulkEmailService
{
    private $sendgrid;

    public function __construct()
    {
        $this->sendgrid = new SendGrid(config('services.sendgrid.api_key'));
    }

    /**
     * Send bulk emails using SendGrid API
     *
     * @param array $emails
     * @param string $templatePath
     * @param string $subject
     * @param array $variables (optional) - variables to replace in template
     * @return array
     */
    public function sendBulkEmails(array $emails, string $templatePath, string $subject, array $variables = [])
    {
        $templateName = basename($templatePath, '.html');
        $results = [
            'total' => count($emails),
            'sent' => 0,
            'skipped' => 0,
            'failed' => 0,
            'errors' => []
        ];

        // Merge with global variables from config
        $globalVariables = config('bulk-emails.variables', []);
        $variables = array_merge($globalVariables, $variables);

        // Load and prepare template
        $htmlContent = $this->loadTemplate($templatePath, $variables);
        if (!$htmlContent) {
            throw new \Exception("Template not found: {$templatePath}");
        }

        foreach ($emails as $email) {
            try {
                // Check if email already sent
                if (EmailLog::isEmailSent($email, $templateName)) {
                    $results['skipped']++;
                    Log::info("Email already sent, skipping: {$email}");
                    continue;
                }

                // Send email
                $this->sendSingleEmail($email, $subject, $htmlContent);
                
                // Log success
                EmailLog::logSuccess($email, $templateName, $subject);
                $results['sent']++;
                
                Log::info("Email sent successfully to: {$email}");
                
                // Small delay to avoid rate limiting
                usleep(100000); // 0.1 second
                
            } catch (\Exception $e) {
                $errorMessage = $e->getMessage();
                
                // Log failure
                EmailLog::logFailure($email, $templateName, $subject, $errorMessage);
                $results['failed']++;
                $results['errors'][] = "Failed to send to {$email}: {$errorMessage}";
                
                Log::error("Failed to send email to {$email}: {$errorMessage}");
            }
        }

        return $results;
    }

    /**
     * Send single email using SendGrid
     */
    private function sendSingleEmail(string $email, string $subject, string $htmlContent)
    {
        $mail = new SendGridMail();

        // Set sender info
        $mail->setFrom(config('mail.from.address'), config('mail.from.name'));
        $mail->setSubject($subject);
        $mail->addTo($email);

        // Add both HTML and plain text versions to avoid spam
        $mail->addContent("text/html", $htmlContent);
        $mail->addContent("text/plain", $this->htmlToPlainText($htmlContent));

        // Add headers to improve deliverability
        $mail->addHeader("List-Unsubscribe", "<mailto:<EMAIL>>");
        $mail->addHeader("X-Mailer", "VietnamWebSummit-Mailer");
        $mail->addHeader("Precedence", "bulk");

        // Set reply-to
        $mail->setReplyTo(config('mail.from.address'), config('mail.from.name'));

        $response = $this->sendgrid->send($mail);

        if ($response->statusCode() >= 400) {
            throw new \Exception("SendGrid API error: " . $response->body());
        }
    }

    /**
     * Convert HTML to plain text for better deliverability
     */
    private function htmlToPlainText($html)
    {
        // Remove HTML tags and convert to plain text
        $text = strip_tags($html);
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }

    /**
     * Load HTML template and replace variables
     */
    private function loadTemplate(string $templatePath, array $variables = [])
    {
        $fullPath = resource_path("views/email-templates/{$templatePath}");
        
        if (!file_exists($fullPath)) {
            return false;
        }

        $content = file_get_contents($fullPath);
        
        // Replace variables in template
        foreach ($variables as $key => $value) {
            $content = str_replace("{{" . $key . "}}", $value, $content);
        }

        return $content;
    }

    /**
     * Get email sending statistics
     */
    public function getStats(?string $templateName = null)
    {
        $query = EmailLog::query();
        
        if ($templateName) {
            $query->where('template_name', $templateName);
        }

        return [
            'total' => $query->count(),
            'sent' => $query->where('status', 'sent')->count(),
            'failed' => $query->where('status', 'failed')->count(),
        ];
    }

    /**
     * Reset email logs for a template (for testing purposes)
     */
    public function resetLogs(string $templateName)
    {
        return EmailLog::where('template_name', $templateName)->delete();
    }
}
