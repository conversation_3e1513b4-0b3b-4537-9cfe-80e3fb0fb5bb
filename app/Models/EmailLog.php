<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EmailLog extends Model
{
    protected $fillable = [
        'email',
        'template_name',
        'subject',
        'status',
        'error_message',
        'sent_at',
    ];

    protected $casts = [
        'sent_at' => 'datetime',
    ];

    public static function isEmailSent($email, $templateName)
    {
        return self::where('email', $email)
            ->where('template_name', $templateName)
            ->where('status', 'sent')
            ->exists();
    }

    public static function logSuccess($email, $templateName, $subject)
    {
        return self::updateOrCreate(
            [
                'email' => $email,
                'template_name' => $templateName,
            ],
            [
                'subject' => $subject,
                'status' => 'sent',
                'error_message' => null,
                'sent_at' => now(),
            ]
        );
    }

    public static function logFailure($email, $templateName, $subject, $errorMessage)
    {
        return self::updateOrCreate(
            [
                'email' => $email,
                'template_name' => $templateName,
            ],
            [
                'subject' => $subject,
                'status' => 'failed',
                'error_message' => $errorMessage,
                'sent_at' => null,
            ]
        );
    }
}
