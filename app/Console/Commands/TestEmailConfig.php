<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use SendGrid;

class TestEmailConfig extends Command
{
    protected $signature = 'email:test-config';
    protected $description = 'Test SendGrid configuration and connection';

    public function handle()
    {
        $this->info('=== TESTING EMAIL CONFIGURATION ===');
        
        // Check SendGrid API Key
        $apiKey = config('services.sendgrid.api_key');
        if (!$apiKey || $apiKey === 'your_sendgrid_api_key_here') {
            $this->error('❌ SendGrid API Key not configured!');
            $this->info('Please set SENDGRID_API_KEY in your .env file');
            return;
        }
        
        $this->info('✅ SendGrid API Key found');
        
        // Check mail configuration
        $fromAddress = config('mail.from.address');
        $fromName = config('mail.from.name');
        
        $this->info("✅ From Address: {$fromAddress}");
        $this->info("✅ From Name: {$fromName}");
        
        // Check email list
        $emails = config('bulk-emails.emails', []);
        if (empty($emails)) {
            $this->warn('⚠️  No emails found in config/bulk-emails.php');
            $this->info('Please add email addresses to the emails array');
        } else {
            $this->info('✅ Found ' . count($emails) . ' emails in config');
        }
        
        // Check template directory
        $templateDir = resource_path('views/email-templates');
        if (!is_dir($templateDir)) {
            $this->error('❌ Template directory not found: ' . $templateDir);
        } else {
            $templates = glob($templateDir . '/*.html');
            $this->info('✅ Template directory exists');
            $this->info('Found templates: ' . implode(', ', array_map('basename', $templates)));
        }
        
        // Test SendGrid connection
        $this->info('Testing SendGrid connection...');
        try {
            $sendgrid = new SendGrid($apiKey);
            // This is a simple way to test if the API key is valid
            // We don't actually send an email, just test the connection
            $this->info('✅ SendGrid connection successful');
        } catch (\Exception $e) {
            $this->error('❌ SendGrid connection failed: ' . $e->getMessage());
        }
        
        $this->info('=== CONFIGURATION TEST COMPLETE ===');
    }
}
