<?php

namespace App\Console\Commands;

use App\Services\BulkEmailService;
use Illuminate\Console\Command;

class SendTestEmail extends Command
{
    protected $signature = 'email:send-test
                            {email : Test email address}
                            {--template=vws-2025-improved.html : Template to use}
                            {--subject=Vietnam Web Summit 2025 - Test Email : Email subject}';

    protected $description = 'Send a test email to check deliverability';

    private $bulkEmailService;

    public function __construct(BulkEmailService $bulkEmailService)
    {
        parent::__construct();
        $this->bulkEmailService = $bulkEmailService;
    }

    public function handle()
    {
        $email = $this->argument('email');
        $template = $this->option('template');
        $subject = $this->option('subject');

        $this->info('=== SENDING TEST EMAIL ===');
        $this->info("To: {$email}");
        $this->info("Template: {$template}");
        $this->info("Subject: {$subject}");

        if (!$this->confirm('Send test email?')) {
            $this->info('Cancelled.');
            return;
        }

        try {
            $variables = config('bulk-emails.variables', []);
            $results = $this->bulkEmailService->sendBulkEmails([$email], $template, $subject, $variables);

            if ($results['sent'] > 0) {
                $this->info('✅ Test email sent successfully!');
                $this->info('Please check:');
                $this->info('  - Inbox folder');
                $this->info('  - Spam/Junk folder');
                $this->info('  - Promotions tab (Gmail)');
                $this->newLine();
                $this->info('💡 Tips:');
                $this->info('  - Mark as "Not Spam" if in spam folder');
                $this->info('  - Add sender to contacts');
                $this->info('  - Reply to improve sender reputation');
            } else {
                $this->error('❌ Failed to send test email');
                if (!empty($results['errors'])) {
                    foreach ($results['errors'] as $error) {
                        $this->error("  - {$error}");
                    }
                }
            }

        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
        }
    }
}
