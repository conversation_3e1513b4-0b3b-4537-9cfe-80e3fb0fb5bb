<?php

namespace App\Console\Commands;

use App\Services\BulkEmailService;
use Illuminate\Console\Command;

class WarmupEmailSender extends Command
{
    protected $signature = 'email:warmup
                            {--emails=5 : Number of emails to send for warmup}
                            {--template=ultra-safe.html : Template to use}';

    protected $description = 'Warm up email sender reputation with small batches';

    private $bulkEmailService;

    public function __construct(BulkEmailService $bulkEmailService)
    {
        parent::__construct();
        $this->bulkEmailService = $bulkEmailService;
    }

    public function handle()
    {
        $emailCount = (int) $this->option('emails');
        $template = $this->option('template');

        $this->info('=== EMAIL WARMUP STRATEGY ===');
        $this->info("Sending {$emailCount} emails with template: {$template}");

        // Load email list
        $allEmails = config('bulk-emails.emails', []);
        if (empty($allEmails)) {
            $this->error('No emails found in config!');
            return;
        }

        // Take only the requested number of emails
        $emails = array_slice($allEmails, 0, $emailCount);

        $this->info("Selected emails: " . implode(', ', $emails));

        if (!$this->confirm('Proceed with warmup sending?')) {
            return;
        }

        try {
            $subject = "Vietnam Web Summit 2025 - Thông báo sự kiện";
            $variables = config('bulk-emails.variables', []);

            $results = $this->bulkEmailService->sendBulkEmails($emails, $template, $subject, $variables);

            $this->info('=== WARMUP RESULTS ===');
            $this->info("Sent: {$results['sent']}");
            $this->info("Failed: {$results['failed']}");
            $this->info("Skipped: {$results['skipped']}");

            if ($results['sent'] > 0) {
                $this->info("\n✅ Warmup batch sent successfully!");
                $this->info("💡 Recommendations:");
                $this->info("  - Wait 24-48 hours before next batch");
                $this->info("  - Monitor bounce rates in SendGrid");
                $this->info("  - Check if emails land in inbox");
                $this->info("  - Gradually increase batch size");
            }

        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
        }
    }
}
